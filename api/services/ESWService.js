const swUtilsService = require("../lib/swUtils");
const PoolsService = require("./esw/_PoolsService");
const MatchesService = require("./esw/_MatchesService");
const eswUtilsService = require("./esw/_UtilsService");
const { ENTRY_STATUSES } = require('../constants/teams');

class ESWService {
    constructor(swUtilsService, eswUtilsService) {
        this.swUtilsService = swUtilsService;
        this.poolsService = new PoolsService(swUtilsService, eswUtilsService);
        this.matchesService = new MatchesService(swUtilsService, eswUtilsService);
    }

    async getEventInfo (eventId) {
        const isESWId = this.swUtilsService.isESWId(eventId);

        eventId = isESWId ? eventId : parseInt(eventId, 10);

        if (!eventId) {
            throw { validation: 'Invalid Event Identifier' };
        }

        const query = this._getEventInfoSQL(isESWId);

        const {rows: [eventInfo]} = await Db.query(query, [eventId]);

        return EventUtils.formatEventInfoFees(eventInfo) || {};
    }

    _getEventInfoSQL (useESWId) {
        return (
            `SELECT (
                    CASE 
			            WHEN e.schedule_published IS TRUE 
			                THEN e.esw_id 
			            ELSE e.event_id::VARCHAR 
			        END
			    ) "event_id", e.event_id "id", e.name, e.long_name, e.reg_fee, e.hide_seeds,
			    to_char(e.date_start, 'MM/DD/YYYY') date_start, to_char(e.date_end, 'MM/DD/YYYY') date_end,
			    e.timezone, e.has_coed_teams, e.has_female_teams, e.has_male_teams, e.email, e.website, e.event_notes,
			    e.city, e.state, e.address, e.schedule_published, e.registration_method, e.has_match_barcodes, 
			    COALESCE(e.teams_settings, '{}'::JSONB) AS teams_settings, epos.point_of_sales_id,
			    COALESCE(e.require_match_end_time, eo.require_match_end_time, FALSE) require_match_end_time,
			    (EXTRACT(EPOCH FROM e.date_reg_open) * 1000)::BIGINT date_reg_open, e.allow_teams_registration, e.social_links,
                (e.tickets_settings->>'allow_point_of_sales')::BOOLEAN IS TRUE "allow_point_of_sales",
			    (EXTRACT(EPOCH FROM e.date_reg_close) * 1000)::BIGINT date_reg_close,
			    (
                    SELECT true FROM roster_team rt
                    WHERE rt.event_id = e.event_id
                        AND (rt.extra->>'prev_qual')::BOOLEAN IS TRUE
                        AND rt.deleted IS NULL
                    LIMIT 1
                ) IS TRUE "is_with_prev_qual",
                (
                    SELECT ARRAY_TO_JSON(ARRAY(
                        SELECT DISTINCT((secs_start :: DATE)) FROM matches
                        WHERE matches.event_id = e.event_id AND matches.day > 0
                        ORDER BY secs_start
                    ))
                ) "match_days",
			    (EXTRACT(EPOCH FROM e.roster_deadline) * 1000)::BIGINT roster_deadline, (
			        CASE 
			            WHEN (
			                    e.registration_method = 'doubles' AND 
			                    e.published IS TRUE AND 
			                    e.teams_use_clubs_module IS TRUE AND 
			                    (COALESCE(MAX(d.date_reg_close), e.date_reg_close) >= (NOW() AT TIME ZONE e.timezone))
			                ) 
			                THEN TRUE 
			            ELSE FALSE 
			        END
			    ) "doubles_reg_available", e.event_tickets_code "tickets_code", 
			    (
			        SELECT ARRAY_TO_JSON(ARRAY(
			            SELECT DISTINCT((secs_start :: DATE)) FROM matches
                        WHERE matches.event_id = e.event_id AND matches.day > 0
                        ORDER BY secs_start
			        ))
			    ) "days",
			    COALESCE(
                    e.allow_ticket_sales IS TRUE  
                    AND e.tickets_published IS TRUE  
                    AND e.event_tickets_code IS NOT NULL 
                    AND (NOW() AT TIME ZONE e.timezone) < e.tickets_purchase_date_end  
                    AND (NOW() AT TIME ZONE e.timezone) > e.tickets_purchase_date_start  
                    AND e.tickets_purchase_date_end <= e.date_end
                    AND e.deleted IS NULL
                    AND e.live_to_public IS TRUE
			    , FALSE) :: BOOLEAN "tickets_published", (
			        CASE 
			            WHEN (e."has_rosters" IS TRUE) 
			                THEN (
			                    SELECT (SUM(d."count") > 0)
			                    FROM (
			                        SELECT COALESCE(COUNT(*), 0) "count"
			                        FROM "roster_athlete" ra 
			                        WHERE ra."event_id" = e.event_id
			                            AND ra."deleted" IS NULL 
			                            AND ra."deleted_by_user" IS NULL
			                        UNION ALL
			                        SELECT COALESCE(COUNT(*), 0) "count"
			                        FROM "roster_staff_role" rsr 
			                        INNER JOIN "roster_team" rt 
			                            ON rt.roster_team_id = rsr.roster_team_id
			                        WHERE rsr."deleted" IS NULl 
			                            AND rsr."deleted_by_user" IS null
			                            AND rt."event_id" = e.event_id
			                    ) "d"
			                )
			            ELSE FALSE
			        END
			    ) "has_rosters", e.teams_use_clubs_module IS TRUE "has_clubs",
			    (
			        SELECT array_to_json(array_agg(fees.reg_fee)) FROM
                    (
                        SELECT distinct(dv.reg_fee) FROM division "dv"
                        WHERE dv.event_id = e.event_id AND dv.reg_fee <> 0 AND dv.closed IS NULL AND dv.published IS TRUE
                    ) as fees
                ) as division_fees,
                (
                    SELECT name AS sport_sanctioning FROM sport_sanctioning
                    WHERE sport_sanctioning_id = e.sport_sanctioning_id
                ),
                (
                    SELECT COALESCE(ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON("l"))), '[]' :: JSON)
                    FROM (
                        SELECT
                            el.name location_name,
                            el.address,
                            el.city,
                            el.state,
                            el.zip,
                            el.number     
                        FROM "event_location" "el"
                        WHERE e.event_id = el.event_id
                        ORDER BY el.number
                    ) l
                ) locations
			FROM "event" e
			LEFT JOIN event_owner eo 
                ON eo.event_owner_id = e.event_owner_id
		    LEFT JOIN event_point_of_sales epos 
		        ON epos.sw_event_id = e.event_id 
		               AND epos.point_of_sales_type = 'tickets'	    
			LEFT JOIN "division" d
			    ON d.event_id = e.event_id
			    AND d.closed IS NULL
			    AND d.locked IS NOT TRUE
			    AND d.published IS TRUE
			WHERE e.live_to_public IS TRUE
                AND ${useESWId ? 'e.esw_id' : 'e.event_id'} = $1
			GROUP BY e.event_id, eo.event_owner_id, epos.point_of_sales_id`
        );
    }

    async getPrevQualTeams (eventId) {
        if(!eventId) {
            throw{ validation: 'Invalid Event Identifier' };
        }

        let query =
            `SELECT
              rt.extra ->> 'earned_at'                              AS "Earned At",
              rt.team_name                                          AS "Team Name",
              d.name                                                AS "Division",
              rt.organization_code                                  AS "Team Code",
              (rt.extra ->> 'prev_qual_age' :: TEXT) || ' ' 
              || INITCAP(rt.extra ->> 'prev_qual_division' :: TEXT) AS "Bid Earned"
            FROM "roster_team" AS rt
              INNER JOIN "division" AS d ON (d.division_id = rt.division_id)
            WHERE ((rt.extra ->> 'prev_qual') :: BOOLEAN IS TRUE) 
                AND (rt.event_id = $1)
                AND rt.deleted IS NULL
            ORDER BY d.name, rt.organization_code`;

        const {rows = []} = await Db.query(query, [eventId]);

        return rows;
    }

    async getManualClubs (eventId) {
        if(!eventId) {
            throw { validation: 'Event Id required' };
        }

        const query = knex('roster_team AS rt')
            .distinct('rt.manual_club_name AS club_name')
            .join('event AS e', 'e.event_id', 'rt.event_id')
            .where('e.esw_id', eventId)
            .whereRaw(`NULLIF(rt.manual_club_name, '') IS NOT NULL`);

        const {rows = []} = await Db.query(query);

        return rows;
    }

    async getClubTeams ({eventId, clubId, clubName}) {
        const clubIdentifier = clubId || clubName;

        const query =
            `SELECT rt.* , d.short_name as division_short_name, 
                ds.matches_won, ds.matches_lost, 
                ds.sets_won, ds.sets_lost, ds.rank as ds_rank, 
                (
                    SELECT array_to_json(array_agg(row_to_json(union_matches))) 
                    FROM (
                        (SELECT * FROM (
                            (SELECT 'team1' match_type,  m.match_id, 
                                extract(epoch from m.secs_finished)::BIGINT as unix_finished, 
                                m.results, m.display_name, 
                                to_char(m.secs_start, 'Dy DD HH12:MI AM') date_start_formatted,  
                                extract(epoch from m.secs_start)::BIGINT * 1000 date_start,  
                                m.division_id, m.division_short_name, c.name court_name, pb.display_name pool_name,  
                                m.pool_bracket_id, pb.is_pool,  
                                rt2.team_name opponent_team_name, rt2.organization_code opponent_organization_code,  
                                pb.name pb_name, r.name round_name 
                            FROM matches m 
                            INNER JOIN poolbrackets pb ON m.pool_bracket_id = pb.uuid  
                            LEFT JOIN courts c ON c.uuid = m.court_id  
                            LEFT JOIN roster_team rt2  
                                ON m.team2_roster_id = rt2.roster_team_id AND rt2.status_entry = ${ENTRY_STATUSES.ACCEPTED}
                            LEFT JOIN rounds r ON r.uuid = m.round_id 
                            WHERE m.team1_roster_id = rt.roster_team_id  
                                AND m.secs_finished IS NULL 
                            ORDER BY date_start ASC LIMIT 1)      
    
                            UNION
    
                            (SELECT 'team2' match_type, m.match_id, 
                                extract(epoch from m.secs_finished)::BIGINT as unix_finished, 
                                m.results, m.display_name,  
                                to_char(m.secs_start, 'Dy DD HH12:MI AM') date_start_formatted,  
                                extract(epoch from m.secs_start)::BIGINT * 1000 date_start,  
                                m.division_id, m.division_short_name, c.name court_name, pb.display_name pool_name,  
                                m.pool_bracket_id, pb.is_pool,  
                                rt2.team_name opponent_team_name, rt2.organization_code opponent_organization_code,  
                                pb.name pb_name, r.name round_name 
                            FROM matches m 
                            INNER JOIN poolbrackets pb ON m.pool_bracket_id = pb.uuid  
                            LEFT JOIN courts c ON c.uuid = m.court_id  
                            LEFT JOIN roster_team rt2  
                                ON m.team1_roster_id = rt2.roster_team_id AND rt2.status_entry = ${ENTRY_STATUSES.ACCEPTED}
                            LEFT JOIN rounds r ON r.uuid = m.round_id 
                            WHERE m.team2_roster_id = rt.roster_team_id  
                                AND m.secs_finished IS NULL 
                            ORDER BY date_start ASC LIMIT 1)
                        ) team_matches ORDER BY date_start ASC LIMIT 1)
    
                        UNION 
    
                        (SELECT 'ref' match_type, m.match_id, 
                            extract(epoch from m.secs_finished)::BIGINT as unix_finished, 
                            m.results, m.display_name,  
                            to_char(m.secs_start, 'Dy DD HH12:MI AM') date_start_formatted,  
                            extract(epoch from m.secs_start)::BIGINT * 1000 date_start,  
                            m.division_id, m.division_short_name, c.name court_name, pb.display_name pool_name, 
                            m.pool_bracket_id, pb.is_pool,  
                            rt2.team_name opponent_team_name, rt2.organization_code opponent_organization_code,  
                            pb.name pb_name, r.name round_name 
                        FROM matches m 
                        INNER JOIN poolbrackets pb ON m.pool_bracket_id = pb.uuid  
                        LEFT JOIN courts c ON c.uuid = m.court_id  
                        LEFT JOIN roster_team rt2  
                            ON m.team1_roster_id = rt2.roster_team_id AND rt2.status_entry = ${ENTRY_STATUSES.ACCEPTED}
                        LEFT JOIN rounds r ON r.uuid = m.round_id 
                        WHERE m.ref_roster_id = rt.roster_team_id 
                            AND m.secs_finished IS NULL 
                        ORDER BY date_start ASC LIMIT 1)      
    
                        ORDER BY date_start ASC 
                    ) union_matches 
                ) AS upcoming 
            FROM roster_team rt 
            INNER JOIN "event" e ON e.event_id = rt.event_id  
            LEFT JOIN division d ON d.division_id = rt.division_id 
            LEFT JOIN division_standing ds ON rt.division_id = ds.division_id AND ds.team_id = rt.roster_team_id 
            WHERE e.esw_id = $1 
                AND rt.deleted IS NULL 
                AND rt.status_entry = ${ENTRY_STATUSES.ACCEPTED}
                AND ${clubId ? 'rt.roster_club_id = $2' : 'rt.manual_club_name = $2'} 
            ORDER BY d.sort_order, d.gender, d.max_age DESC, d.level_sort_order, d.level`;

        const {rows = []} = await Db.query(query, [eventId, clubIdentifier]);

        return rows;
    }

    async getPoolStandings(poolId) {
        if(!this.swUtilsService.isUUID(poolId)) {
            throw { validation: 'Invalid pool id' };
        }

        const [pool, standings] = await Promise.all([
            this.poolsService.getPoolDataWithDivision(poolId),
            this.matchesService.getStandings(poolId)
        ]);

        return {
            pool,
            standings
        }
    }

    async getBracketMatches(eventId, bracketId) {
        if(!this.swUtilsService.isESWId(eventId)) {
            throw { validation: 'Invalid Event Identifier passed' };
        }

        if(!this.swUtilsService.isUUID(bracketId)) {
            throw { validation: 'Invalid bracket identifier passed' };
        }

        const poolBracket = await this.poolsService.getPoolBracket(bracketId);

        const matches = await this.matchesService.getBracketMatches(poolBracket);

        return {
            pool: poolBracket,
            matches,
            display_name: poolBracket.display_name || false,
            division_name: poolBracket.division_name || false
        }
    }
}

module.exports = new ESWService(swUtilsService, eswUtilsService);
